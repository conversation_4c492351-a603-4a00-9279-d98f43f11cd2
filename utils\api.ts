import { buildApiUrl, getCommonHeaders } from '@/config/api';
import { AuthUtils } from './auth';

/**
 * API Utility functions for making authenticated requests
 */

export class ApiClient {
  /**
   * Make an authenticated API request
   */
  static async request(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const authHeaders = await AuthUtils.getAuthHeader();
    
    const response = await fetch(buildApiUrl(endpoint), {
      ...options,
      headers: {
        ...getCommonHeaders(),
        ...authHeaders,
        ...options.headers,
      },
    });

    // Handle 401 Unauthorized - redirect to login
    if (response.status === 401) {
      await AuthUtils.clearAuth();
      // You might want to redirect to auth screen here
      // router.replace('/auth');
    }

    return response;
  }

  /**
   * GET request
   */
  static async get(endpoint: string): Promise<Response> {
    return this.request(endpoint, { method: 'GET' });
  }

  /**
   * POST request
   */
  static async post(endpoint: string, data: any): Promise<Response> {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * PUT request
   */
  static async put(endpoint: string, data: any): Promise<Response> {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  /**
   * DELETE request
   */
  static async delete(endpoint: string): Promise<Response> {
    return this.request(endpoint, { method: 'DELETE' });
  }
}

/**
 * Example usage:
 * 
 * // Get leads
 * const response = await ApiClient.get('/leads');
 * const leads = await response.json();
 * 
 * // Create a new lead
 * const response = await ApiClient.post('/leads', { name: 'John Doe', email: '<EMAIL>' });
 * 
 * // Update a lead
 * const response = await ApiClient.put('/leads/123', { status: 'contacted' });
 * 
 * // Delete a lead
 * const response = await ApiClient.delete('/leads/123');
 */
