// API Configuration
// Update this with your actual backend URL
export const API_CONFIG = {
  BASE_URL: 'http://192.168.18.214:5000/api', // Change this to your backend URL
  ENDPOINTS: {
    SIGNIN: '/auth/signin',
    SIGNUP: '/auth/signup',
    FORGOT_PASSWORD: '/auth/forgot-password',
    // Add other endpoints as needed
  },
  TIMEOUT: 10000, // 10 seconds
};

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Common headers for API requests
export const getCommonHeaders = () => ({
  'Content-Type': 'application/json',
});
