# Backend TwiML Fix for Single Twilio Number + In-App Agent Calls

## Your Use Case
- **Single Twilio number** calls all leads
- **Agent handles calls** through your app (not personal phone)
- **Lead receives call** from your Twilio number
- **Agent joins call** through the app interface

## Current Problem
Your current `generateTwiML` function is using `.connect().room()` which is correct for this use case, but there might be configuration issues.

## Correct Approach for Your Use Case

### Option 1: Voice SDK Room Connection (Recommended)

```javascript
exports.generateTwiML = async (req, res) => {
  try {
    const { roomName } = req.query;
    
    if (!roomName) {
      return res.status(400).json({ error: 'Room name is required' });
    }

    const VoiceResponse = twilio.twiml.VoiceResponse;
    const twiml = new VoiceResponse();

    // ✅ Connect to Voice SDK room for in-app calling
    twiml.connect()
      .room(roomName);

    res.type('text/xml');
    res.send(twiml.toString());

  } catch (err) {
    console.error('Error generating TwiML:', err);
    res.status(500).json({ error: err.message });
  }
};
```

**Wait!** Your original code was actually correct. The issue might be elsewhere. Let's check:

### Option 2: Simple Voice Message (For Testing)

```javascript
exports.generateTwiML = async (req, res) => {
  try {
    const { roomName } = req.query;
    
    if (!roomName) {
      return res.status(400).json({ error: 'Room name is required' });
    }

    const VoiceResponse = twilio.twiml.VoiceResponse;
    const twiml = new VoiceResponse();

    // ✅ Simple message for testing
    twiml.say('Hello, this is a call from your lead management system.');
    twiml.pause({ length: 2 });
    twiml.say('An agent will join this call shortly.');
    twiml.pause({ length: 1 });
    twiml.say('Please wait.');

    res.type('text/xml');
    res.send(twiml.toString());

  } catch (err) {
    console.error('Error generating TwiML:', err);
    res.status(500).json({ error: err.message });
  }
};
```

## Updated Backend Call Creation

```javascript
exports.createCall = async (req, res) => {
  try {
    const { leadId, agentId } = req.body;
    
    const lead = await Lead.findById(leadId);
    const agent = await Agent.findById(agentId);
    
    if (!lead || !agent) {
      return res.status(404).json({ error: 'Lead or Agent not found' });
    }

    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    // Create a unique room name for this call
    const roomName = `call_${leadId}_${agentId}_${Date.now()}`;
    
    // ✅ URL for Voice SDK room connection
    const twimlUrl = `${process.env.BASE_URL}/api/calls/twiml?roomName=${roomName}`;
    
    console.log('📞 Creating call with:', {
      to: lead.Phone,
      from: process.env.TWILIO_PHONE_NUMBER,
      twimlUrl: twimlUrl,
      roomName: roomName
    });
    
    // Create the call
    const call = await client.calls.create({
      url: twimlUrl,
      to: lead.Phone,
      from: process.env.TWILIO_PHONE_NUMBER,
      statusCallback: `${process.env.BASE_URL}/api/calls/status-callback`,
      statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
      statusCallbackMethod: 'POST'
    });

    console.log('✅ Call created:', call.sid, 'Status:', call.status);

    // Store call information
    const callData = {
      callSid: call.sid,
      roomName,
      leadId,
      agentId,
      leadPhone: lead.Phone,
      agentName: agent.name,
      status: 'initiated',
      createdAt: new Date()
    };

    // Emit call created event to connected clients
    const io = req.app.get('io');
    io.emit('call-created', {
      callSid: call.sid,
      roomName,
      leadId,
      agentId,
      status: 'initiated'
    });

    res.json({
      success: true,
      callSid: call.sid,
      roomName,
      message: 'Call initiated successfully'
    });

  } catch (err) {
    console.error('Error creating call:', err);
    res.status(500).json({ error: err.message });
  }
};
```

## Frontend Integration for Voice SDK

Since you're using Expo, you'll need to handle the Voice SDK differently. Here's the updated approach:

### Updated Calling Service

```typescript
// utils/calling.ts - Updated for Voice SDK
public async joinCallAsAgent(roomName: string, identity: string): Promise<void> {
  try {
    console.log('🎧 Agent joining call:', { roomName, identity });
    
    // Generate access token for Voice SDK
    const accessToken = await this.generateAccessToken(identity, roomName);
    
    // In a real implementation, you'd use Twilio Voice SDK here
    // For now, we'll just emit the event
    console.log('✅ Agent access token generated for room:', roomName);
    
    // Emit agent joined event
    if (this.socket) {
      this.socket.emit('agent-joined-call', { roomName, identity });
    }
    
  } catch (error) {
    console.error('❌ Error joining call as agent:', error);
    throw error;
  }
}
```

### Updated Call Screen

```typescript
// app/call.tsx - Add agent join functionality
const handleJoinCall = async () => {
  if (!callSid || !isCallActive) return;
  
  try {
    // Join the call as an agent
    await callingService.joinCallAsAgent(roomName, agentId);
    
    Alert.alert(
      'Joined Call',
      'You have joined the call as an agent.',
      [{ text: 'OK' }]
    );
    
  } catch (error) {
    console.error('Error joining call:', error);
    Alert.alert('Error', 'Failed to join call as agent.');
  }
};
```

## Troubleshooting Steps

### 1. Test TwiML Endpoint
```bash
curl "http://*************:5000/api/calls/twiml?roomName=test"
```

### 2. Check Phone Number Format
Ensure lead phone numbers are in E.164 format: `+1234567890`

### 3. Verify Twilio Configuration
- TwiML App SID is correct
- Phone number is verified in Twilio
- Webhook URL is accessible

### 4. Test with Simple TwiML First
Use Option 2 (simple voice message) to test if the basic call flow works.

## Recommended Implementation Steps

1. **Start with Option 2** (simple voice message) to test basic call flow
2. **Once that works**, switch to Option 1 (Voice SDK room)
3. **Implement agent join functionality** in your app
4. **Add Voice SDK** for full in-app calling experience

## Voice SDK Limitations in Expo

Note: Full Voice SDK integration requires:
- Ejecting from Expo Go
- Using development builds
- Native Twilio Voice SDK

For now, you can:
1. Use the simple voice message approach
2. Handle call management through your backend
3. Later upgrade to full Voice SDK when needed

**Which approach would you like to try first?** 