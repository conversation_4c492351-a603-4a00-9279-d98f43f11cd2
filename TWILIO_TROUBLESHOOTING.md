# Twi<PERSON> Call Troubleshooting Guide

## Issue: Call Status Shows "Busy" in Twilio Logs

The "Busy" status in Twilio logs indicates that the call is being rejected or not properly handled. Here are the most common causes and solutions:

### 1. TwiML Configuration Issue

**Problem**: Your backend `generateTwiML` function is using `.room()` which is for Twilio Video, not Voice calls.

**Solution**: Update your backend TwiML generation:

```javascript
// INCORRECT (for Video):
exports.generateTwiML = async (req, res) => {
  const VoiceResponse = twilio.twiml.VoiceResponse;
  const twiml = new VoiceResponse();
  twiml.connect().room(roomName); // ❌ This is for Video
  res.type('text/xml');
  res.send(twiml.toString());
};

// CORRECT (for Voice):
exports.generateTwiML = async (req, res) => {
  const VoiceResponse = twilio.twiml.VoiceResponse;
  const twiml = new VoiceResponse();
  
  // For PSTN calls (phone to phone):
  twiml.say('Hello, this is a call from your lead management system.');
  twiml.pause({ length: 1 });
  twiml.say('The call will now be connected.');
  
  // OR for more complex scenarios:
  // twiml.dial({
  //   callerId: process.env.TWILIO_PHONE_NUMBER
  // }, '+**********');
  
  res.type('text/xml');
  res.send(twiml.toString());
};
```

### 2. Phone Number Format Issue

**Problem**: Phone numbers not in E.164 format.

**Solution**: Ensure phone numbers are in international format:

```javascript
// In your backend createCall function:
const call = await client.calls.create({
  url: `${process.env.BASE_URL}/api/calls/twiml?roomName=${roomName}`,
  to: `+1${lead.Phone.replace(/\D/g, '')}`, // Ensure +1 prefix for US numbers
  from: process.env.TWILIO_PHONE_NUMBER,
  // ... other options
});
```

### 3. TwiML App Configuration

**Problem**: TwiML app not properly configured or URL not accessible.

**Solution**: 
1. Check your TwiML app in Twilio Console
2. Ensure the webhook URL is publicly accessible
3. Test the TwiML endpoint directly

### 4. Environment Variables

**Problem**: Missing or incorrect Twilio credentials.

**Solution**: Verify these environment variables in your backend:

```bash
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_API_KEY=SKxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_API_SECRET=your_api_secret
TWILIO_TWIML_APP_SID=APxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_PHONE_NUMBER=+**********
BASE_URL=https://your-backend-domain.com
```

### 5. Webhook URL Accessibility

**Problem**: Twilio can't reach your webhook URL.

**Solution**: 
1. Use ngrok for local development: `ngrok http 5000`
2. Update your BASE_URL to the ngrok URL
3. Ensure your server is accessible from the internet

## Debugging Steps

### 1. Enable Debug Logging

The app now includes comprehensive debug logging. Check your console for:

```
🔌 Connecting to WebSocket: http://192.168.18.14:5000
✅ WebSocket connected successfully
📞 Creating call with: { leadId: "...", agentId: "..." }
📡 Backend response status: 200
✅ Call created successfully: { callSid: "...", roomName: "..." }
```

### 2. Test Backend Endpoints

Test your backend endpoints directly:

```bash
# Test call creation
curl -X POST http://192.168.18.14:5000/api/calls/create \
  -H "Content-Type: application/json" \
  -d '{"leadId":"test","agentId":"test"}'

# Test TwiML generation
curl "http://192.168.18.14:5000/api/calls/twiml?roomName=test"
```

### 3. Check Twilio Logs

In Twilio Console:
1. Go to Monitor → Logs → Calls
2. Find your call SID
3. Check the "Error Code" and "Error Message" fields
4. Look at the "Request" tab to see what TwiML was generated

### 4. Common Error Codes

- **31000**: Invalid URL
- **31001**: Invalid HTTP method
- **31002**: Invalid HTTP status code
- **31003**: Invalid TwiML
- **31004**: Invalid parameter
- **31005**: Invalid parameter value

## Quick Fixes to Try

### 1. Simple TwiML Test

Replace your `generateTwiML` function with this simple version:

```javascript
exports.generateTwiML = async (req, res) => {
  const VoiceResponse = twilio.twiml.VoiceResponse;
  const twiml = new VoiceResponse();
  
  twiml.say('Hello, this is a test call from your lead management system.');
  twiml.pause({ length: 2 });
  twiml.say('Thank you for testing.');
  
  res.type('text/xml');
  res.send(twiml.toString());
};
```

### 2. Check Phone Number

Ensure your test phone number is:
- In E.164 format (+**********)
- A real, verified number
- Not blocked by carrier

### 3. Test with Twilio Test Credentials

Use Twilio's test credentials first:
- Account SID: `AC**********abcdef**********abcdef`
- Auth Token: `**********abcdef**********abcdef`

## Backend Debugging

Add this to your backend call controller:

```javascript
exports.createCall = async (req, res) => {
  try {
    const { leadId, agentId } = req.body;
    console.log('📞 Creating call:', { leadId, agentId });
    
    const lead = await Lead.findById(leadId);
    const agent = await Agent.findById(agentId);
    
    console.log('📞 Lead found:', lead);
    console.log('📞 Agent found:', agent);
    
    if (!lead || !agent) {
      console.log('❌ Lead or Agent not found');
      return res.status(404).json({ error: 'Lead or Agent not found' });
    }

    const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
    
    const roomName = `call_${leadId}_${agentId}_${Date.now()}`;
    const twimlUrl = `${process.env.BASE_URL}/api/calls/twiml?roomName=${roomName}`;
    
    console.log('📞 TwiML URL:', twimlUrl);
    console.log('📞 Calling number:', lead.Phone);
    console.log('📞 From number:', process.env.TWILIO_PHONE_NUMBER);
    
    const call = await client.calls.create({
      url: twimlUrl,
      to: lead.Phone,
      from: process.env.TWILIO_PHONE_NUMBER,
      statusCallback: `${process.env.BASE_URL}/api/calls/status-callback`,
      statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
      statusCallbackMethod: 'POST'
    });

    console.log('✅ Call created:', call.sid);
    console.log('✅ Call status:', call.status);

    // ... rest of your code
  } catch (err) {
    console.error('❌ Error creating call:', err);
    res.status(500).json({ error: err.message });
  }
};
```

## Next Steps

1. **Check the debug logs** in your app console
2. **Test the TwiML endpoint** directly in browser
3. **Verify phone number format** in your database
4. **Check Twilio Console** for detailed error messages
5. **Test with a simple TwiML** first

Let me know what the debug logs show and I can help you identify the specific issue! 