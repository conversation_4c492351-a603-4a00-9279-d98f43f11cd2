import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Send, Paperclip } from 'lucide-react-native';
import { mockLeads } from './(tabs)/index';

export default function EmailScreen() {
  const { leadId } = useLocalSearchParams<{ leadId: string }>();
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [isSending, setIsSending] = useState(false);

  const lead = mockLeads.find(l => l.id === leadId);

  const handleSendEmail = async () => {
    if (!subject.trim() && !body.trim()) {
      Alert.alert('Error', 'Please enter a subject or message body');
      return;
    }

    setIsSending(true);

    // Simulate API call to send email
    setTimeout(() => {
      setIsSending(false);
      Alert.alert(
        'Email Sent',
        'Your email has been sent successfully through our secure email service.',
        [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]
      );
    }, 2000);
  };

  const handleBack = () => {
    router.back();
  };

  const handleAttachment = () => {
    Alert.alert('Attachments', 'Attachment feature would be available in the full version');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Compose Email</Text>
        <TouchableOpacity onPress={handleAttachment} style={styles.attachButton}>
          <Paperclip size={20} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView 
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.leadInfo}>
          <Text style={styles.leadTitle}>To: {lead?.title}</Text>
          <Text style={styles.emailSubtitle}>
            Email will be sent through our secure email service
          </Text>
        </View>

        <ScrollView style={styles.formContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Subject</Text>
            <TextInput
              style={styles.subjectInput}
              placeholder="Enter email subject..."
              value={subject}
              onChangeText={setSubject}
              maxLength={200}
              editable={!isSending}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Message</Text>
            <TextInput
              style={styles.bodyInput}
              placeholder="Type your message here..."
              value={body}
              onChangeText={setBody}
              multiline
              textAlignVertical="top"
              maxLength={5000}
              editable={!isSending}
            />
          </View>

          <View style={styles.characterCount}>
            <Text style={styles.characterCountText}>
              Subject: {subject.length}/200 • Message: {body.length}/5000
            </Text>
          </View>
        </ScrollView>

        <View style={styles.bottomSection}>
          <TouchableOpacity
            style={[styles.sendButton, isSending && styles.sendButtonDisabled]}
            onPress={handleSendEmail}
            disabled={isSending}
            activeOpacity={0.8}
          >
            <Send size={20} color="#FFFFFF" />
            <Text style={styles.sendButtonText}>
              {isSending ? 'Sending...' : 'Send Email'}
            </Text>
          </TouchableOpacity>

          <View style={styles.securityNote}>
            <Text style={styles.securityText}>
              🔒 Emails are sent through our secure email service. 
              Recipient email addresses are never exposed in the app.
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  attachButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  leadInfo: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  leadTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  emailSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
  formContainer: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  subjectInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    color: '#000000',
  },
  bodyInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    color: '#000000',
    minHeight: 200,
  },
  characterCount: {
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  characterCountText: {
    fontSize: 12,
    color: '#8E8E93',
  },
  bottomSection: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  sendButtonDisabled: {
    opacity: 0.6,
  },
  sendButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  securityNote: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  securityText: {
    fontSize: 12,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 16,
  },
});