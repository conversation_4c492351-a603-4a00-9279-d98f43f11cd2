# Backend API Integration

This document explains how the React Native app integrates with your backend signin API.

## Overview

The authentication system has been integrated with your backend API that expects:

```javascript
// Request
POST /api/auth/signin
{
  "email": "<EMAIL>",
  "password": "userpassword"
}

// Response (Success)
{
  "token": "jwt_token_here",
  "agent": {
    "id": "agent_id",
    "name": "Agent Name",
    "email": "<EMAIL>"
  }
}

// Response (Error)
{
  "error": "Error message"
}
```

## Configuration

### 1. Update Backend URL

Edit `config/api.ts` and update the `BASE_URL`:

```typescript
export const API_CONFIG = {
  BASE_URL: 'https://your-backend-domain.com/api', // Update this
  // ... rest of config
};
```

### 2. Authentication Flow

1. User enters email/password in `app/auth.tsx`
2. App sends POST request to your `/api/auth/signin` endpoint
3. Backend validates credentials and returns JWT token + user data
4. App stores token and user data in AsyncStorage
5. User is redirected to main app

### 3. Making Authenticated API Calls

Use the `ApiClient` utility for authenticated requests:

```typescript
import { ApiClient } from '@/utils/api';

// Example: Get user's leads
const response = await ApiClient.get('/leads');
const leads = await response.json();

// Example: Create a new lead
const response = await ApiClient.post('/leads', {
  name: 'John Doe',
  email: '<EMAIL>',
});
```

## Files Modified/Created

- `app/auth.tsx` - Updated signin function to call your API
- `config/api.ts` - API configuration
- `utils/auth.ts` - Authentication utilities
- `utils/api.ts` - API client for authenticated requests
- `app/(tabs)/settings.tsx` - Updated logout to clear stored auth data

## Security Notes

- JWT token is stored in AsyncStorage
- Token is automatically included in API requests via Authorization header
- 401 responses automatically clear stored auth data
- All API calls use HTTPS in production (update BASE_URL accordingly)

## Testing

1. Start your backend server
2. Update `API_CONFIG.BASE_URL` to point to your backend
3. Test signin with valid credentials
4. Verify token storage and authenticated API calls

## Twilio Call Integration

### Call API Integration

The `app/call.tsx` screen now integrates with your Twilio call API:

```javascript
// Backend API endpoint
POST /api/leads/:id/call

// Expected response
{
  "message": "Call initiated",
  "sid": "twilio_call_sid"
}
```

### Call Flow

1. User taps "Start Call" in the call screen
2. App sends POST request to `/api/leads/{leadId}/call`
3. Backend uses Twilio API to initiate call to lead's phone number
4. App displays call status and Twilio call SID
5. User can end the call session in the app

### Features

- Loading states during call initiation
- Error handling for failed calls
- Display of Twilio call SID for tracking
- Secure call initiation through backend
- Call timer for user reference

## Error Handling

The app handles various error scenarios:

- Network errors
- Invalid credentials (400/401 responses)
- Server errors (500+ responses)
- Malformed responses
- Call initiation failures

All errors are displayed to the user via Alert dialogs.
