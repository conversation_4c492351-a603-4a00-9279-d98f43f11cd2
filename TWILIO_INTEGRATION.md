# Twilio Calling Integration

This document explains how the Twilio calling functionality has been integrated into the Expo app.

## Overview

The app now supports real-time voice calling using <PERSON><PERSON><PERSON>'s Voice SDK. The integration includes:

- **Backend Integration**: Uses your existing Twilio call controller
- **Real-time Updates**: WebSocket connection for live call status updates
- **Call Management**: Start, end, and monitor calls
- **Status Tracking**: Real-time call status (initiated, ringing, answered, completed, failed)

## Architecture

### Frontend Components

1. **CallingService** (`utils/calling.ts`)
   - Manages WebSocket connection to backend
   - Handles API calls to Twilio endpoints
   - Provides real-time call status updates

2. **useCall Hook** (`hooks/useCall.ts`)
   - React hook for managing call state
   - Handles call lifecycle (start, end, status updates)
   - Provides call duration timer

3. **Call Screen** (`app/call.tsx`)
   - Updated to use the new calling service
   - Maintains existing UI design
   - Shows real-time connection status

### Backend Integration

The app connects to your existing Twilio call controller endpoints:

- `POST /api/calls/access-token` - Generate Twilio access tokens
- `POST /api/calls/create` - Create new calls
- `POST /api/calls/:callSid/end` - End calls
- `GET /api/calls/history` - Get call history
- WebSocket events for real-time updates

## Setup Instructions

### 1. Install Dependencies

The following packages have been added to `package.json`:

```json
{
  "socket.io-client": "^4.7.4",
  "twilio-voice": "^6.0.0"
}
```

Run the installation:
```bash
npm install
```

### 2. Backend Configuration

Ensure your backend has the following:

1. **WebSocket Server**: Socket.IO server running alongside your Express app
2. **Environment Variables**: 
   - `TWILIO_ACCOUNT_SID`
   - `TWILIO_API_KEY`
   - `TWILIO_API_SECRET`
   - `TWILIO_TWIML_APP_SID`
   - `TWILIO_PHONE_NUMBER`
   - `BASE_URL`

3. **CORS Configuration**: Allow WebSocket connections from your app

### 3. API Configuration

Update `config/api.ts` with your backend URL:

```typescript
export const API_CONFIG = {
  BASE_URL: 'http://your-backend-url:5000/api',
  // ... other config
};
```

### 4. Agent Authentication

In a production app, you'll need to:

1. Implement agent authentication
2. Pass the actual agent ID instead of the mock `agent_123`
3. Store agent credentials securely

## Usage

### Starting a Call

1. Navigate to a lead detail page
2. Tap the call button
3. The app will:
   - Connect to WebSocket server
   - Create a call via your backend
   - Show real-time status updates
   - Display call duration when answered

### Call Status Flow

1. **Initiating** → Call creation in progress
2. **Initiated** → Call created, waiting for lead to answer
3. **Ringing** → Lead's phone is ringing
4. **Answered** → Call is active, timer starts
5. **Completed/Failed** → Call ended

### Real-time Features

- **Connection Status**: WiFi icon shows WebSocket connection status
- **Call Status**: Real-time updates from Twilio webhooks
- **Call Duration**: Timer starts when call is answered
- **Automatic Cleanup**: Calls are properly ended when leaving the screen

## Error Handling

The integration includes comprehensive error handling:

- **Connection Errors**: Shows alerts for WebSocket connection issues
- **Call Failures**: Displays specific error messages from backend
- **Network Issues**: Graceful handling of network interruptions
- **State Management**: Proper cleanup of call state

## Security Features

- **Secure API Calls**: All calls go through your authenticated backend
- **Token Generation**: Access tokens generated server-side
- **Call Logging**: All calls logged for quality assurance
- **Phone Protection**: Lead phone numbers not exposed to frontend

## Testing

### Local Development

1. Start your backend server with WebSocket support
2. Update the API URL in `config/api.ts`
3. Test call initiation with a valid lead ID
4. Monitor WebSocket connection status

### Production Considerations

1. **SSL/TLS**: Use HTTPS/WSS for production
2. **Environment Variables**: Secure storage of Twilio credentials
3. **Rate Limiting**: Implement call rate limiting
4. **Monitoring**: Add logging and monitoring for call quality

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check backend URL configuration
   - Ensure CORS is properly configured
   - Verify backend WebSocket server is running

2. **Call Creation Fails**
   - Check Twilio credentials in backend
   - Verify phone number format
   - Check backend logs for specific errors

3. **No Real-time Updates**
   - Verify WebSocket connection status
   - Check backend webhook configuration
   - Ensure Socket.IO events are properly emitted

### Debug Mode

Enable debug logging by adding to your app:

```typescript
// In calling.ts
console.log('WebSocket events:', this.socket?.connected);
console.log('Call status:', status);
```

## Future Enhancements

Potential improvements for the calling system:

1. **Call Recording**: Add call recording functionality
2. **Call Transfer**: Support for call transfers
3. **Call Notes**: Real-time call notes during conversation
4. **Call Analytics**: Detailed call analytics and reporting
5. **Multi-party Calls**: Support for conference calls
6. **Call Scheduling**: Schedule calls for later
7. **Call Templates**: Pre-defined call scripts

## Support

For issues with the Twilio integration:

1. Check the Twilio documentation
2. Review backend logs for errors
3. Verify all environment variables are set
4. Test WebSocket connection separately
5. Check network connectivity and firewall settings 