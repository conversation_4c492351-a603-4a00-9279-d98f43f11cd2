import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { ChevronRight } from 'lucide-react-native';

// Mock leads data - in production, this would come from a secure API
const mockLeads = [
  { id: '1', title: 'Real Estate Inquiry - Downtown', email: '<EMAIL>', phone: '+1234567890' },
  { id: '2', title: 'Insurance Quote Request', email: '<EMAIL>', phone: '+1234567891' },
  { id: '3', title: 'Investment Consultation', email: '<EMAIL>', phone: '+1234567892' },
  { id: '4', title: 'Home Loan Application', email: '<EMAIL>', phone: '+1234567893' },
  { id: '5', title: 'Business Insurance Inquiry', email: '<EMAIL>', phone: '+1234567894' },
  { id: '6', title: 'Property Valuation Request', email: '<EMAIL>', phone: '+1234567895' },
  { id: '7', title: 'Financial Planning Session', email: '<EMAIL>', phone: '+1234567896' },
  { id: '8', title: 'Mortgage Refinancing', email: '<EMAIL>', phone: '+1234567897' },
];

export default function LeadsListScreen() {
  const handleLeadPress = (lead: typeof mockLeads[0]) => {
    // Navigate to lead detail screen with only the lead ID
    // This ensures no sensitive data is passed through navigation params
    router.push({
      pathname: '/lead-detail',
      params: { leadId: lead.id }
    });
  };

  const renderLeadItem = ({ item }: { item: typeof mockLeads[0] }) => (
    <TouchableOpacity
      style={styles.leadCard}
      onPress={() => handleLeadPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.leadContent}>
        <Text style={styles.leadTitle}>{item.title}</Text>
        <Text style={styles.leadDate}>
          {new Date().toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </Text>
      </View>
      <ChevronRight size={20} color="#C7C7CC" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Leads</Text>
        <Text style={styles.headerSubtitle}>{mockLeads.length} active leads</Text>
      </View>
      
      <FlatList
        data={mockLeads}
        renderItem={renderLeadItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: '#000000',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#8E8E93',
  },
  listContainer: {
    padding: 16,
  },
  leadCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  leadContent: {
    flex: 1,
  },
  leadTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  leadDate: {
    fontSize: 14,
    color: '#8E8E93',
  },
});

// Export mock leads for use in other screens
export { mockLeads };