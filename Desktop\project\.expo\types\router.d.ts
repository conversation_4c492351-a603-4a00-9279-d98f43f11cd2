/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/auth`; params?: Router.UnknownInputParams; } | { pathname: `/call`; params?: Router.UnknownInputParams; } | { pathname: `/chat`; params?: Router.UnknownInputParams; } | { pathname: `/email`; params?: Router.UnknownInputParams; } | { pathname: `/lead-detail`; params?: Router.UnknownInputParams; } | { pathname: `/../utils/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../config/api`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/auth`; params?: Router.UnknownOutputParams; } | { pathname: `/call`; params?: Router.UnknownOutputParams; } | { pathname: `/chat`; params?: Router.UnknownOutputParams; } | { pathname: `/email`; params?: Router.UnknownOutputParams; } | { pathname: `/lead-detail`; params?: Router.UnknownOutputParams; } | { pathname: `/../utils/auth`; params?: Router.UnknownOutputParams; } | { pathname: `/../config/api`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/auth${`?${string}` | `#${string}` | ''}` | `/call${`?${string}` | `#${string}` | ''}` | `/chat${`?${string}` | `#${string}` | ''}` | `/email${`?${string}` | `#${string}` | ''}` | `/lead-detail${`?${string}` | `#${string}` | ''}` | `/../utils/auth${`?${string}` | `#${string}` | ''}` | `/../config/api${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/auth`; params?: Router.UnknownInputParams; } | { pathname: `/call`; params?: Router.UnknownInputParams; } | { pathname: `/chat`; params?: Router.UnknownInputParams; } | { pathname: `/email`; params?: Router.UnknownInputParams; } | { pathname: `/lead-detail`; params?: Router.UnknownInputParams; } | { pathname: `/../utils/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../config/api`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
