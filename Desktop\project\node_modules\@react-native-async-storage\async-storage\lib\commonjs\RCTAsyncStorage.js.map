{"version": 3, "names": ["_reactNative", "require", "_shouldFallbackToLegacyNativeModule", "RCTAsyncStorage", "TurboModuleRegistry", "get", "NativeModules", "shouldFallbackToLegacyNativeModule", "_default", "exports", "default"], "sourceRoot": "../../src", "sources": ["RCTAsyncStorage.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,mCAAA,GAAAD,OAAA;AAEA;AACA;AACA;AACA,IAAIE,eAAe,GAAGC,gCAAmB,GACrCA,gCAAmB,CAACC,GAAG,CAAC,sBAAsB,CAAC;AAAI;AACnDD,gCAAmB,CAACC,GAAG,CAAC,0BAA0B,CAAC,IACnDD,gCAAmB,CAACC,GAAG,CAAC,iBAAiB,CAAC,GAC1CC,0BAAa,CAAC,sBAAsB,CAAC;AAAI;AACzCA,0BAAa,CAAC,0BAA0B,CAAC,IACzCA,0BAAa,CAAC,iBAAiB,CAAC;AAEpC,IAAI,CAACH,eAAe,IAAI,IAAAI,sEAAkC,EAAC,CAAC,EAAE;EAC5D,IAAIH,gCAAmB,EAAE;IACvBD,eAAe,GACbC,gCAAmB,CAACC,GAAG,CAAC,sBAAsB,CAAC,IAC/CD,gCAAmB,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAChD,CAAC,MAAM;IACLF,eAAe,GACbG,0BAAa,CAAC,sBAAsB,CAAC,IACrCA,0BAAa,CAAC,mBAAmB,CAAC;EACtC;AACF;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcP,eAAe", "ignoreList": []}