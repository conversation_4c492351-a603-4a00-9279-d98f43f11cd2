import { useState, useEffect, useCallback } from 'react';
import { callingService, CallStatus, CallData } from '@/utils/calling';
import { Alert } from 'react-native';

export interface UseCallReturn {
  isCallActive: boolean;
  isInitiatingCall: boolean;
  callDuration: number;
  callSid: string | null;
  callStatus: string;
  isSocketConnected: boolean;
  startCall: (leadId: string, agentId: string) => Promise<void>;
  endCall: () => Promise<void>;
  formatTime: (seconds: number) => string;
}

export const useCall = (): UseCallReturn => {
  const [isCallActive, setIsCallActive] = useState(false);
  const [isInitiatingCall, setIsInitiatingCall] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [callSid, setCallSid] = useState<string | null>(null);
  const [callStatus, setCallStatus] = useState('idle');
  const [isSocketConnected, setIsSocketConnected] = useState(false);

  // Timer effect for call duration
  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (isCallActive && callStatus === 'answered') {
      console.log('⏱️ Starting call duration timer');
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    return () => {
      if (interval) {
        console.log('⏱️ Stopping call duration timer');
        clearInterval(interval);
      }
    };
  }, [isCallActive, callStatus]);

  // Socket connection status
  useEffect(() => {
    const checkConnection = () => {
      const connected = callingService.isSocketConnected();
      if (connected !== isSocketConnected) {
        console.log('🔌 Socket connection status changed:', connected);
        setIsSocketConnected(connected);
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 5000);
    return () => clearInterval(interval);
  }, [isSocketConnected]);

  // Call status listener
  useEffect(() => {
    console.log('👂 Setting up call status listener for callSid:', callSid);
    
    const unsubscribe = callingService.addCallStatusListener((status: CallStatus) => {
      console.log('📞 Call status received in hook:', status);
      console.log('📞 Current callSid:', callSid);
      console.log('📞 Status callSid:', status.callSid);
      
      if (status.callSid === callSid) {
        console.log('✅ Status matches current call, updating state');
        setCallStatus(status.status);
        
        switch (status.status) {
          case 'initiated':
            console.log('📞 Call initiated');
            setIsCallActive(true);
            setIsInitiatingCall(false);
            break;
          case 'ringing':
            console.log('📞 Call ringing');
            setCallStatus('ringing');
            break;
          case 'answered':
            console.log('📞 Call answered, starting timer');
            setCallStatus('answered');
            setCallDuration(0); // Start timer when call is answered
            break;
          case 'completed':
          case 'failed':
            console.log('📞 Call ended:', status.status);
            setIsCallActive(false);
            setCallDuration(0);
            setCallStatus('idle');
            setCallSid(null);
            break;
        }
      } else {
        console.log('❌ Status callSid does not match current call');
      }
    });

    return unsubscribe;
  }, [callSid]);

  const startCall = useCallback(async (leadId: string, agentId: string) => {
    console.log('🚀 Starting call process:', { leadId, agentId });
    
    if (!isSocketConnected) {
      console.log('❌ Socket not connected, showing alert');
      Alert.alert(
        'Connection Error',
        'Not connected to server. Please check your internet connection and try again.'
      );
      return;
    }

    setIsInitiatingCall(true);
    setCallStatus('initiating');
    console.log('📞 Call state set to initiating');

    try {
      const callData: CallData = await callingService.createCall(leadId, agentId);
      console.log('✅ Call data received:', callData);
      
      setCallSid(callData.callSid);
      setIsCallActive(true);
      setCallStatus('initiated');
      console.log('📞 Call state updated to initiated');
      
      Alert.alert(
        'Call Initiated',
        'The call has been initiated. The lead will receive a call shortly.',
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('❌ Call initiation error:', error);
      setIsCallActive(false);
      setCallStatus('idle');
      setCallSid(null);
      
      Alert.alert(
        'Call Failed',
        error instanceof Error ? error.message : 'Failed to initiate call. Please try again.'
      );
    } finally {
      setIsInitiatingCall(false);
      console.log('📞 Call initiation process completed');
    }
  }, [isSocketConnected]);

  const endCall = useCallback(async () => {
    console.log('🛑 Ending call process, callSid:', callSid);
    
    if (!callSid) {
      console.log('📞 No callSid, just resetting state');
      setIsCallActive(false);
      setCallDuration(0);
      setCallStatus('idle');
      return;
    }

    try {
      await callingService.endCall(callSid);
      console.log('✅ Call end request sent successfully');
      
      setIsCallActive(false);
      setCallDuration(0);
      setCallStatus('idle');
      setCallSid(null);
      
    } catch (error) {
      console.error('❌ Error ending call:', error);
      Alert.alert(
        'Error',
        'Failed to end call properly. Please try again.'
      );
    }
  }, [callSid]);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    isCallActive,
    isInitiatingCall,
    callDuration,
    callSid,
    callStatus,
    isSocketConnected,
    startCall,
    endCall,
    formatTime,
  };
}; 