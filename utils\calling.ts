import { ApiClient } from './api';
import { io, Socket } from 'socket.io-client';
import { API_CONFIG } from '@/config/api';

export interface CallStatus {
  callSid: string;
  status: 'initiated' | 'ringing' | 'answered' | 'completed' | 'failed';
  roomName?: string;
  leadId?: string;
  agentId?: string;
}

export interface CallData {
  callSid: string;
  roomName: string;
  leadId: string;
  agentId: string;
  status: string;
}

class CallingService {
  private socket: Socket | null = null;
  private callStatusListeners: ((status: CallStatus) => void)[] = [];
  private isConnected = false;

  constructor() {
    this.initializeSocket();
  }

  private initializeSocket() {
    try {
      // Connect to your backend WebSocket server
      const socketUrl = API_CONFIG.BASE_URL.replace('/api', '');
      console.log('🔌 Connecting to WebSocket:', socketUrl);
      
      this.socket = io(socketUrl, {
        transports: ['websocket', 'polling'],
        autoConnect: true,
      });

      this.socket.on('connect', () => {
        console.log('✅ WebSocket connected successfully');
        this.isConnected = true;
      });

      this.socket.on('disconnect', () => {
        console.log('❌ WebSocket disconnected');
        this.isConnected = false;
      });

      this.socket.on('connect_error', (error) => {
        console.error('🔌 WebSocket connection error:', error);
      });

      this.socket.on('call-status-update', (data: CallStatus) => {
        console.log('📞 Call status update received:', data);
        this.notifyCallStatusListeners(data);
      });

      this.socket.on('call-created', (data: CallData) => {
        console.log('📞 Call created event received:', data);
        this.notifyCallStatusListeners({
          callSid: data.callSid,
          status: 'initiated',
          roomName: data.roomName,
          leadId: data.leadId,
          agentId: data.agentId,
        });
      });

      this.socket.on('call-ended', (data: { callSid: string }) => {
        console.log('📞 Call ended event received:', data);
        this.notifyCallStatusListeners({
          callSid: data.callSid,
          status: 'completed',
        });
      });

    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error);
    }
  }

  private notifyCallStatusListeners(status: CallStatus) {
    console.log('📢 Notifying call status listeners:', status);
    this.callStatusListeners.forEach(listener => listener(status));
  }

  public addCallStatusListener(listener: (status: CallStatus) => void) {
    this.callStatusListeners.push(listener);
    return () => {
      const index = this.callStatusListeners.indexOf(listener);
      if (index > -1) {
        this.callStatusListeners.splice(index, 1);
      }
    };
  }

  public async generateAccessToken(identity: string, roomName: string): Promise<string> {
    try {
      console.log('🔑 Generating access token for:', { identity, roomName });
      
      const response = await ApiClient.post('/calls/access-token', {
        identity,
        roomName,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Access token generation failed:', errorData);
        throw new Error(errorData.error || 'Failed to generate access token');
      }

      const data = await response.json();
      console.log('✅ Access token generated successfully');
      return data.accessToken;
    } catch (error) {
      console.error('❌ Error generating access token:', error);
      throw error;
    }
  }

  public async createCall(leadId: string, agentId: string): Promise<CallData> {
    try {
      console.log('📞 Creating call with:', { leadId, agentId });
      
      const response = await ApiClient.post('/calls/create', {
        leadId,
        agentId,
      });

      console.log('📡 Backend response status:', response.status);
      console.log('📡 Backend response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Call creation failed:', errorData);
        throw new Error(errorData.error || 'Failed to create call');
      }

      const data = await response.json();
      console.log('✅ Call created successfully:', data);
      
      return {
        callSid: data.callSid,
        roomName: data.roomName,
        leadId,
        agentId,
        status: 'initiated',
      };
    } catch (error) {
      console.error('❌ Error creating call:', error);
      throw error;
    }
  }

  public async endCall(callSid: string): Promise<void> {
    try {
      console.log('📞 Ending call:', callSid);
      
      const response = await ApiClient.post(`/calls/${callSid}/end`, {});

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Call end failed:', errorData);
        throw new Error(errorData.error || 'Failed to end call');
      }

      console.log('✅ Call ended successfully');
    } catch (error) {
      console.error('❌ Error ending call:', error);
      throw error;
    }
  }

  public async getCallHistory(leadId?: string, agentId?: string): Promise<any[]> {
    try {
      const params = new URLSearchParams();
      if (leadId) params.append('leadId', leadId);
      if (agentId) params.append('agentId', agentId);

      const response = await ApiClient.get(`/calls/history?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get call history');
      }

      const data = await response.json();
      return data.calls || [];
    } catch (error) {
      console.error('Error getting call history:', error);
      throw error;
    }
  }

  public isSocketConnected(): boolean {
    return this.isConnected;
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.callStatusListeners = [];
    this.isConnected = false;
  }
}

// Export a singleton instance
export const callingService = new CallingService(); 