import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Phone, PhoneOff, Wifi, WifiOff } from 'lucide-react-native';
import { mockLeads } from './(tabs)/index';
import { useCall } from '@/hooks/useCall';

export default function CallScreen() {
  const { leadId } = useLocalSearchParams<{ leadId: string }>();
  const {
    isCallActive,
    isInitiatingCall,
    callDuration,
    callSid,
    callStatus,
    isSocketConnected,
    startCall,
    endCall,
    formatTime,
  } = useCall();

  const lead = mockLeads.find(l => l.id === leadId);

  // Mock agent ID - in a real app, this would come from authentication context
  const agentId = '6853e8adf0bde88556990365';

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      if (isCallActive) {
        endCall();
      }
    };
  }, [isCallActive, endCall]);

  const handleStartCall = async () => {
    if (!leadId) {
      Alert.alert('Error', 'Lead information not found');
      return;
    }

    await startCall(leadId, agentId);
  };

  const handleEndCall = () => {
    endCall();
    router.back();
  };

  const handleBack = () => {
    if (isCallActive) {
      handleEndCall();
    } else {
      router.back();
    }
  };

  const getStatusText = () => {
    if (!isSocketConnected) {
      return 'Connecting to server...';
    }
    
    switch (callStatus) {
      case 'initiating':
        return 'Initiating call...';
      case 'initiated':
        return 'Call initiated via Twilio';
      case 'ringing':
        return 'Calling lead...';
      case 'answered':
        return 'Call in progress';
      case 'completed':
        return 'Call completed';
      case 'failed':
        return 'Call failed';
      default:
        return 'Ready to call';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Voice Call</Text>
        <View style={styles.connectionStatus}>
          {isSocketConnected ? (
            <Wifi size={16} color="#34C759" />
          ) : (
            <WifiOff size={16} color="#FF3B30" />
          )}
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.callInfo}>
          <Text style={styles.leadTitle}>{lead?.title}</Text>
          <Text style={styles.callStatus}>{getStatusText()}</Text>
          {isCallActive && callStatus === 'answered' && (
            <Text style={styles.callTimer}>{formatTime(callDuration)}</Text>
          )}
          {callSid && (
            <Text style={styles.callSid}>Call ID: {callSid}</Text>
          )}
        </View>

        <View style={styles.callControls}>
          {!isCallActive ? (
            <TouchableOpacity
              style={[
                styles.startCallButton,
                (isInitiatingCall || !isSocketConnected) && styles.buttonDisabled
              ]}
              onPress={handleStartCall}
              activeOpacity={0.8}
              disabled={isInitiatingCall || !isSocketConnected}
            >
              <Phone size={32} color="#FFFFFF" />
              <Text style={styles.startCallText}>
                {isInitiatingCall ? 'Calling...' : 'Start Call'}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.endCallButton}
              onPress={handleEndCall}
              activeOpacity={0.8}
            >
              <PhoneOff size={32} color="#FFFFFF" />
              <Text style={styles.endCallText}>End Call</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.securityNote}>
          <Text style={styles.securityText}>
            🔒 Calls are initiated securely through Twilio's API via our backend.
            Lead phone numbers are protected and calls are logged for quality assurance.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C1C1E',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  connectionStatus: {
    width: 40,
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  callInfo: {
    alignItems: 'center',
    marginTop: 60,
  },
  leadTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  callStatus: {
    fontSize: 18,
    color: '#8E8E93',
    marginBottom: 24,
  },
  callTimer: {
    fontSize: 48,
    fontWeight: '300',
    color: '#FFFFFF',
    fontVariant: ['tabular-nums'],
  },
  callSid: {
    fontSize: 12,
    color: '#8E8E93',
    marginTop: 8,
    fontFamily: 'monospace',
  },
  callControls: {
    alignItems: 'center',
    marginBottom: 60,
  },
  startCallButton: {
    backgroundColor: '#34C759',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#34C759',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  endCallButton: {
    backgroundColor: '#FF3B30',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FF3B30',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  endCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  securityNote: {
    backgroundColor: 'rgba(142, 142, 147, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  securityText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 20,
  },
});