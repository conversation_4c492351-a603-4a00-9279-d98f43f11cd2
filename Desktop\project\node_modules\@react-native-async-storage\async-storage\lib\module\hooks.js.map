{"version": 3, "names": ["AsyncStorage", "useAsyncStorage", "key", "getItem", "args", "setItem", "mergeItem", "removeItem"], "sourceRoot": "../../src", "sources": ["hooks.ts"], "mappings": ";;AAAA,OAAOA,YAAY,MAAM,gBAAgB;AAGzC,OAAO,SAASC,eAAeA,CAACC,GAAW,EAAoB;EAC7D,OAAO;IACLC,OAAO,EAAEA,CAAC,GAAGC,IAAI,KAAKJ,YAAY,CAACG,OAAO,CAACD,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDC,OAAO,EAAEA,CAAC,GAAGD,IAAI,KAAKJ,YAAY,CAACK,OAAO,CAACH,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDE,SAAS,EAAEA,CAAC,GAAGF,IAAI,KAAKJ,YAAY,CAACM,SAAS,CAACJ,GAAG,EAAE,GAAGE,IAAI,CAAC;IAC5DG,UAAU,EAAEA,CAAC,GAAGH,IAAI,KAAKJ,YAAY,CAACO,UAAU,CAACL,GAAG,EAAE,GAAGE,IAAI;EAC/D,CAAC;AACH", "ignoreList": []}