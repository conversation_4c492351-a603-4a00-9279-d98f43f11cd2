import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Phone, PhoneOff } from 'lucide-react-native';
import { mockLeads } from './(tabs)/index';

export default function CallScreen() {
  const { leadId } = useLocalSearchParams<{ leadId: string }>();
  const [isCallActive, setIsCallActive] = useState(false);
  const [callDuration, setCallDuration] = useState(0);

  const lead = mockLeads.find(l => l.id === leadId);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isCallActive) {
      interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartCall = () => {
    setIsCallActive(true);
    setCallDuration(0);
  };

  const handleEndCall = () => {
    setIsCallActive(false);
    setCallDuration(0);
    router.back();
  };

  const handleBack = () => {
    if (isCallActive) {
      handleEndCall();
    } else {
      router.back();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Voice Call</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        <View style={styles.callInfo}>
          <Text style={styles.leadTitle}>{lead?.title}</Text>
          <Text style={styles.callStatus}>
            {isCallActive ? 'Call in progress' : 'Ready to call'}
          </Text>
          {isCallActive && (
            <Text style={styles.callTimer}>{formatTime(callDuration)}</Text>
          )}
        </View>

        <View style={styles.callControls}>
          {!isCallActive ? (
            <TouchableOpacity
              style={styles.startCallButton}
              onPress={handleStartCall}
              activeOpacity={0.8}
            >
              <Phone size={32} color="#FFFFFF" />
              <Text style={styles.startCallText}>Start Call</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.endCallButton}
              onPress={handleEndCall}
              activeOpacity={0.8}
            >
              <PhoneOff size={32} color="#FFFFFF" />
              <Text style={styles.endCallText}>End Call</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.securityNote}>
          <Text style={styles.securityText}>
            🔒 All call details are handled securely through our backend system. 
            Client phone numbers are never exposed in the app.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C1C1E',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  callInfo: {
    alignItems: 'center',
    marginTop: 60,
  },
  leadTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  callStatus: {
    fontSize: 18,
    color: '#8E8E93',
    marginBottom: 24,
  },
  callTimer: {
    fontSize: 48,
    fontWeight: '300',
    color: '#FFFFFF',
    fontVariant: ['tabular-nums'],
  },
  callControls: {
    alignItems: 'center',
    marginBottom: 60,
  },
  startCallButton: {
    backgroundColor: '#34C759',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#34C759',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  endCallButton: {
    backgroundColor: '#FF3B30',
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FF3B30',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  endCallText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 8,
  },
  securityNote: {
    backgroundColor: 'rgba(142, 142, 147, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  securityText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 20,
  },
});